#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Created on Wed Jul  2 16:36:05 2025

@author: armangetzen

This script contains functions to run analytics on open slots, rescheduable
patients, and movement opportunities to support data reports for Operation
Bird Dog.

"""

import pandas as pd
import os
import json
import random
from ast import literal_eval
import math
from datetime import date, time, datetime, timedelta
import numpy as np

class OBDResults:
    
    # Initialization of Class Variables
    def __init__(self):
        return
    
    def initialize_data_sources(self, data_dir, dept_string):
        # Same as the results directory that the OBDScheduler outputs are saved to
        self.data_dir = data_dir
        # Load in reschedule opportunities, list of eligible patients, and all slots overall in the dept
        self.slots_opportunities = pd.read_csv(f"{data_dir}/{dept_string}_slot_reschedule_opportunities.csv")
        self.patient_list = pd.read_csv(f"{data_dir}/{dept_string}_mrn_list.csv")
        # Slots data only includes slots within the slot horizon, metrics will explicitly mention this
        self.slots_data = pd.read_csv(f"{data_dir}/{dept_string}_all_slots_data.csv")

        return


    def calculate_OBD_metrics(self, slot_horizon):
        
        self.slots_data["Utilization"] = self.slots_data["NUM_APTS_SCHEDULED"]/self.slots_data["ORG_REG_OPENINGS"]
                                                                                                
                                                                                                
        #slot_horizon
        
        
        # calculate utilization, by clinic, by type (borrown from OBD agenda logic)
        # calculate number of opportunities found
        
        return
    
    def store_OBD_metrics():
        return
        # generate a report for them to go along with the call reports
        